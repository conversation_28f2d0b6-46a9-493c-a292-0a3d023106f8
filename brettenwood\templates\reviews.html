{% extends "base.html" %}

{% block content %}
<section class="reviews-header">
    <div class="container">
        <h1>Customer Reviews</h1>
        <p>See what our clients say about our services</p>
    </div>
</section>

<section class="reviews-container">
    <div class="leave-review">
        <h2>Share Your Experience</h2>
        <form method="POST" action="{{ url_for('reviews') }}" class="review-form">
            <div class="form-group">
                <label for="name">Your Name</label>
                <input type="text" id="name" name="name" required>
            </div>
            <div class="form-group">
                <label>Your Rating</label>
                <div class="star-rating">
                    {% for i in range(5, 0, -1) %}
                    <input type="radio" id="star{{ i }}" name="rating" value="{{ i }}" {% if loop.first %}required{% endif %}>
                    <label for="star{{ i }}"><i class="far fa-star"></i></label>
                    {% endfor %}
                </div>
            </div>
            <div class="form-group">
                <label for="comment">Your Review</label>
                <textarea id="comment" name="comment" rows="5" required></textarea>
            </div>
            <button type="submit" class="submit-review">Submit Review</button>
        </form>
    </div>

    <div class="customer-reviews">
        <h2>What Our Customers Say</h2>
        {% if reviews %}
        <div class="reviews-grid">
            {% for review in reviews %}
            <div class="review-card">
                <div class="review-header">
                    <h3>{{ review.name }}</h3>
                    <div class="review-stars">
                        {% for i in range(5) %}
                        {% if i < review.rating %}
                        <i class="fas fa-star"></i>
                        {% else %}
                        <i class="far fa-star"></i>
                        {% endif %}
                        {% endfor %}
                    </div>
                </div>
                <p class="review-text">{{ review.comment }}</p>
                <div class="review-date">{{ review.date.strftime('%d %B %Y') if review.date else "Recently" }}</div>
            </div>
            {% endfor %}
        </div>
        {% else %}
        <div class="no-reviews">
            <p>No reviews yet. Be the first to share your experience!</p>
        </div>
        {% endif %}
    </div>
</section>
{% endblock %}