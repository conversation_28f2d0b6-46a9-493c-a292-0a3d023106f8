/* ===== BRETTENWOOD PROJECTS - BLACK & WHITE THEME ===== */
/* Mobile-First Responsive Design */

/* CSS Variables for consistent theming */
:root {
    --black: #000000;
    --white: #ffffff;
    --gray-light: #f8f9fa;
    --gray-medium: #6c757d;
    --gray-dark: #343a40;
    --shadow: rgba(0, 0, 0, 0.1);
    --shadow-hover: rgba(0, 0, 0, 0.2);
    --transition: all 0.3s ease;
    --border-radius: 8px;
    --header-height: 70px;
    --max-width: 1200px;
}

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    line-height: 1.6;
    color: var(--black);
    background-color: var(--white);
    overflow-x: hidden;
}

/* Container */
.container {
    max-width: var(--max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: 2rem; }
h2 { font-size: 1.75rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }

p {
    margin-bottom: 1rem;
    color: var(--gray-dark);
}

a {
    color: var(--black);
    text-decoration: none;
    transition: var(--transition);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background: var(--black);
    color: var(--white);
    border: 2px solid var(--black);
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    cursor: pointer;
    transition: var(--transition);
    text-decoration: none;
    font-size: 1rem;
    min-height: 48px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

.btn:hover {
    background: var(--white);
    color: var(--black);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px var(--shadow-hover);
}

.btn-outline {
    background: var(--white);
    color: var(--black);
}

.btn-outline:hover {
    background: var(--black);
    color: var(--white);
}

/* Header */
header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: var(--header-height);
    background: var(--white);
    border-bottom: 1px solid var(--gray-light);
    z-index: 1000;
    display: flex;
    align-items: center;
    padding: 0 1rem;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    max-width: var(--max-width);
    margin: 0 auto;
}

/* Logo */
.logo {
    font-size: 1.5rem;
    font-weight: 900;
    color: var(--black);
    text-decoration: none;
}

.logo-sub {
    font-size: 0.8rem;
    color: var(--gray-medium);
    font-weight: 400;
    margin-left: 0.5rem;
}

/* Navigation - Mobile First */
.nav-menu {
    position: fixed;
    top: var(--header-height);
    left: -100%;
    width: 100%;
    height: calc(100vh - var(--header-height));
    background: var(--white);
    display: flex;
    flex-direction: column;
    padding: 2rem 1rem;
    transition: var(--transition);
    list-style: none;
    border-top: 1px solid var(--gray-light);
}

.nav-menu.active {
    left: 0;
}

.nav-menu li {
    margin-bottom: 1rem;
}

.nav-link {
    display: block;
    padding: 1rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--black);
    border-radius: var(--border-radius);
    transition: var(--transition);
    min-height: 48px;
    display: flex;
    align-items: center;
}

.nav-link:hover {
    background: var(--gray-light);
}

/* Hamburger Menu */
.hamburger {
    display: flex;
    flex-direction: column;
    cursor: pointer;
    padding: 0.5rem;
    z-index: 1001;
}

.hamburger span {
    width: 25px;
    height: 3px;
    background: var(--black);
    margin: 3px 0;
    transition: var(--transition);
    border-radius: 2px;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

/* Main Content */
main {
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
}

.main-content {
    margin-top: var(--header-height);
    min-height: calc(100vh - var(--header-height));
}

/* Sections */
section {
    padding: 3rem 1rem;
}

.section-header {
    text-align: center;
    margin-bottom: 3rem;
}

.section-header h1,
.section-header h2 {
    margin-bottom: 1rem;
}

.section-header p {
    font-size: 1.1rem;
    color: var(--gray-medium);
    max-width: 600px;
    margin: 0 auto;
}

/* Cards */
.card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    margin-bottom: 2rem;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-hover);
}

.card-image {
    width: 100%;
    height: 200px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.card-content {
    padding: 1.5rem;
}

.card-title {
    font-size: 1.25rem;
    margin-bottom: 0.5rem;
}

.card-text {
    color: var(--gray-medium);
    margin-bottom: 1rem;
}

/* Grid System */
.grid {
    display: grid;
    gap: 2rem;
    grid-template-columns: 1fr;
}

/* Footer */
footer {
    background: var(--black);
    color: var(--white);
    padding: 3rem 1rem 1rem;
    text-align: center;
}

.footer-content {
    max-width: var(--max-width);
    margin: 0 auto;
}

.footer-section {
    margin-bottom: 2rem;
}

.footer-section h3 {
    margin-bottom: 1rem;
    color: var(--white);
}

.footer-section p,
.footer-section a {
    color: var(--gray-light);
}

.footer-section a:hover {
    color: var(--white);
}

.footer-bottom {
    border-top: 1px solid var(--gray-dark);
    padding-top: 1rem;
    margin-top: 2rem;
    color: var(--gray-medium);
}

/* Utility Classes */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }

/* ===== PAGE-SPECIFIC STYLES ===== */

/* Hero Section (Home Page) */
.hero {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 4rem 1rem;
    min-height: 60vh;
    background: linear-gradient(135deg, var(--gray-light) 0%, var(--white) 100%);
}

.hero-content {
    max-width: 800px;
    margin-bottom: 2rem;
}

.hero-title {
    font-size: 2.5rem;
    font-weight: 900;
    margin-bottom: 1rem;
    color: var(--black);
}

.hero-subtitle {
    font-size: 1.2rem;
    color: var(--gray-medium);
    margin-bottom: 2rem;
}

.hero-cta {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    align-items: center;
}

.cta-button {
    background: var(--black);
    color: var(--white);
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    min-width: 200px;
    text-align: center;
}

.cta-button:hover {
    background: var(--gray-dark);
    transform: translateY(-2px);
}

.secondary-button {
    background: var(--white);
    color: var(--black);
    border: 2px solid var(--black);
    padding: 1rem 2rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: var(--transition);
    min-width: 200px;
    text-align: center;
}

.secondary-button:hover {
    background: var(--black);
    color: var(--white);
}

/* Features Section */
.features {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 3rem 1rem;
}

.feature-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px var(--shadow);
    text-align: center;
    transition: var(--transition);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-hover);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    color: var(--black);
}

.feature-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--black);
}

.feature-text {
    color: var(--gray-medium);
}

/* Systems Page */
.systems-header {
    text-align: center;
    padding: 2rem 1rem;
    background: var(--gray-light);
}

.brand-toggle {
    display: flex;
    justify-content: center;
    padding: 2rem 1rem;
    background: var(--white);
}

.brand-toggle-inner {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    background: var(--gray-light);
    padding: 0.5rem;
    border-radius: 50px;
}

.brand-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    background: transparent;
    color: var(--gray-medium);
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    min-height: 44px;
}

.brand-btn:hover {
    background: var(--white);
    color: var(--black);
}

.brand-btn.active {
    background: var(--black);
    color: var(--white);
}

.systems-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 1rem;
}

.system-card {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px var(--shadow);
    overflow: hidden;
    transition: var(--transition);
    position: relative;
}

.system-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-hover);
}

.system-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--black);
    color: var(--white);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
}

.system-image-container {
    height: 250px;
    position: relative;
    overflow: hidden;
    background: var(--gray-light);
}

.system-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.system-details {
    padding: 1.5rem;
}

.system-details h2 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--black);
}

.specs {
    margin-bottom: 1.5rem;
}

.specs p {
    margin-bottom: 0.5rem;
    color: var(--gray-medium);
}

.specs strong {
    color: var(--black);
}

.price-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--black);
}

.inquiry-button {
    background: var(--black);
    color: var(--white);
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.inquiry-button:hover {
    background: var(--gray-dark);
    transform: translateY(-2px);
}

/* Portfolio Gallery */
.portfolio-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 1rem;
}

.portfolio-item {
    background: var(--white);
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px var(--shadow);
    overflow: hidden;
    transition: var(--transition);
}

.portfolio-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-hover);
}

.portfolio-image {
    width: 100%;
    height: 250px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.portfolio-content {
    padding: 1.5rem;
}

.portfolio-location {
    font-weight: bold;
    color: var(--black);
    margin-bottom: 0.5rem;
}

.portfolio-description {
    color: var(--gray-medium);
}

/* Reviews Section */
.reviews-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 1rem;
}

.review-card {
    background: var(--white);
    padding: 2rem;
    border-radius: var(--border-radius);
    box-shadow: 0 2px 10px var(--shadow);
    transition: var(--transition);
}

.review-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px var(--shadow-hover);
}

.review-rating {
    display: flex;
    gap: 0.25rem;
    margin-bottom: 1rem;
}

.star {
    color: var(--black);
    font-size: 1.2rem;
}

.review-text {
    color: var(--gray-dark);
    margin-bottom: 1rem;
    font-style: italic;
}

.review-author {
    font-weight: bold;
    color: var(--black);
}

/* Contact Form */
.contact-form {
    max-width: 600px;
    margin: 0 auto;
    padding: 2rem 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: var(--black);
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--gray-light);
    border-radius: var(--border-radius);
    font-size: 1rem;
    transition: var(--transition);
    font-family: inherit;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--black);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

/* ===== RESPONSIVE DESIGN ===== */

/* Tablet Styles */
@media (min-width: 768px) {
    .container {
        padding: 0 2rem;
    }

    .hero-title {
        font-size: 3rem;
    }

    .hero-cta {
        flex-direction: row;
        justify-content: center;
    }

    .features {
        grid-template-columns: repeat(2, 1fr);
    }

    .systems-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .portfolio-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .reviews-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .hamburger {
        display: none;
    }

    .nav-menu {
        position: static;
        width: auto;
        height: auto;
        background: transparent;
        flex-direction: row;
        padding: 0;
        border-top: none;
        left: 0;
    }

    .nav-menu li {
        margin-bottom: 0;
        margin-left: 2rem;
    }

    .nav-link {
        padding: 0.5rem 1rem;
        font-size: 1rem;
        min-height: auto;
    }

    .hero-title {
        font-size: 3.5rem;
    }

    .features {
        grid-template-columns: repeat(3, 1fr);
    }

    .systems-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .portfolio-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .reviews-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .footer-content {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 2rem;
        text-align: left;
    }
}

/* Large Desktop */
@media (min-width: 1200px) {
    .hero-title {
        font-size: 4rem;
    }

    section {
        padding: 4rem 1rem;
    }
}

/* Touch Device Optimizations */
@media (hover: none) and (pointer: coarse) {
    .btn:hover,
    .card:hover,
    .system-card:hover,
    .portfolio-item:hover,
    .review-card:hover {
        transform: none;
    }

    .nav-link:hover {
        background: transparent;
    }
}