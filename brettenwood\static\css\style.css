/* Base Styles with Mobile Considerations */
:root {
    --primary: #000000;
    --secondary: #ffffff;
    --accent: #555555;
    --light-gray: #f5f5f5;
    --dark-gray: #333333;
    --transition: all 0.3s ease;
    --header-height: 70px; /* Added for consistent mobile header */
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent; /* Remove tap highlight on mobile */
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    color: var(--primary);
    background-color: var(--secondary);
    line-height: 1.6;
    overflow-x: hidden; /* Prevent horizontal scroll */
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
}

/* Improved Touch Targets */
a, button, .nav-link {
    min-width: 44px;
    min-height: 44px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* Header with Mobile-First Approach */
header {
    background-color: var(--secondary);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 1000;
    padding: 1rem 5%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: var(--header-height);
}

/* Mobile Menu Styles */
nav ul {
    position: fixed;
    top: var(--header-height);
    left: -100%;
    width: 100%;
    height: calc(100vh - var(--header-height));
    background-color: var(--secondary);
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: var(--transition);
    padding: 2rem 0;
    overflow-y: auto;
}

    nav ul.active {
        left: 0;
    }

    nav ul li {
        margin: 1.5rem 0;
    }

.hamburger {
    display: block;
    z-index: 1001;
    background: none;
    border: none;
    padding: 10px;
}

    .hamburger span {
        display: block;
        width: 25px;
        height: 3px;
        background-color: var(--primary);
        margin: 5px 0;
        transition: var(--transition);
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

/* Hero Section Mobile Optimization */
.hero {
    flex-direction: column;
    padding: calc(var(--header-height) + 2rem) 5% 3rem;
    min-height: auto;
}

.hero-content {
    padding-right: 0;
    margin-bottom: 3rem;
    order: 1;
}

.hero-title {
    font-size: 2.2rem;
}

.hero-image {
    order: 2;
    height: 300px;
    width: 100%;
    margin-bottom: 2rem;
}

/* Button Sizing for Touch */
.cta-button, .secondary-button {
    padding: 1rem 2rem;
    min-width: 160px;
}

/* Features Grid for Mobile */
.features {
    flex-direction: column;
    padding: 3rem 5%;
}

.feature-card {
    margin: 0 0 2rem 0;
}

/* Testimonials Mobile Layout */
.testimonial-preview {
    padding: 3rem 5%;
}

.testimonial-slider {
    flex-direction: column;
}

.testimonial {
    margin: 0 0 2rem 0;
    max-width: 100%;
}

/* Systems Page Mobile Layout */
.systems-header {
    padding: calc(var(--header-height) + 2rem) 5% 2rem;
    text-align: center;
}

.systems-header h1 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary);
}

.systems-header p {
    color: var(--accent);
    font-size: 1.1rem;
}

/* Brand Toggle Styles */
.brand-toggle {
    display: flex;
    justify-content: center;
    padding: 2rem 5% 1rem;
    margin-bottom: 2rem;
}

.brand-toggle-inner {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    background: var(--light-gray);
    padding: 0.5rem;
    border-radius: 50px;
}

.brand-btn {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
    border: none;
    background: transparent;
    color: var(--accent);
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    min-width: 80px;
}

.brand-btn:hover {
    background: var(--secondary);
    color: var(--primary);
}

.brand-btn.active {
    background: var(--primary);
    color: var(--secondary);
}

/* Current Special Section */
.current-special {
    background: linear-gradient(135deg, var(--primary), var(--dark-gray));
    color: var(--secondary);
    margin: 2rem 5%;
    padding: 2rem;
    border-radius: 15px;
    position: relative;
    overflow: hidden;
}

.special-badge {
    background: #ff6b35;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    display: inline-block;
    margin-bottom: 1rem;
}

.special-content {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.special-details h2 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
}

.special-details ul {
    list-style: none;
    padding: 0;
}

.special-details li {
    padding: 0.3rem 0;
    position: relative;
    padding-left: 1.5rem;
}

.special-details li:before {
    content: "✓";
    position: absolute;
    left: 0;
    color: #4CAF50;
    font-weight: bold;
}

.special-price {
    margin-top: 1rem;
}

.old-price {
    text-decoration: line-through;
    color: #ccc;
    margin-right: 1rem;
}

.special-price .price {
    font-size: 1.8rem;
    font-weight: bold;
    color: #4CAF50;
}

.special-price small {
    display: block;
    margin-top: 0.5rem;
    color: #ccc;
}

/* Systems Grid */
.systems-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 5%;
}

/* System Card Styles */
.system-card {
    background: var(--secondary);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    position: relative;
}

.system-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.system-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary);
    color: var(--secondary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
}

.system-image-container {
    height: 250px;
    position: relative;
    overflow: hidden;
}

.system-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.system-details {
    padding: 1.5rem;
}

.system-details h2 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--primary);
}

.specs {
    margin-bottom: 1.5rem;
}

.specs p {
    margin-bottom: 0.5rem;
    color: var(--accent);
}

.specs strong {
    color: var(--primary);
}

.price-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary);
}

.inquiry-button {
    background: var(--primary);
    color: var(--secondary);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.inquiry-button:hover {
    background: var(--dark-gray);
    transform: translateY(-2px);
}

/* Filtration Section */
.filtration-section {
    padding: 3rem 5%;
    background: var(--light-gray);
}

.filtration-card {
    background: var(--secondary);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
}

.filtration-image-container {
    position: relative;
    height: 250px;
    overflow: hidden;
}

.filtration-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.filtration-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: #ff6b35;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
}

.filtration-content {
    padding: 2rem;
}

.filtration-title {
    font-size: 1.5rem;
    margin-bottom: 1rem;
    color: var(--primary);
}

.filtration-content p {
    color: var(--accent);
    margin-bottom: 1.5rem;
    line-height: 1.6;
}

.filtration-specs {
    margin-bottom: 2rem;
}

.spec-item {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
    gap: 1rem;
}

.spec-item i {
    color: #4CAF50;
    font-size: 1.2rem;
}

.spec-item span {
    color: var(--accent);
}

.spec-item strong {
    color: var(--primary);
}

.filtration-cta {
    background: #ff6b35;
    color: white;
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    display: inline-block;
}

.filtration-cta:hover {
    background: #e55a2b;
    transform: translateY(-2px);
}

/* Portfolio Mobile Layout */
.portfolio-header {
    padding: calc(var(--header-height) + 2rem) 5% 2rem;
    margin-top: 0;
}

.square-gallery {
    grid-template-columns: 1fr;
}

/* Reviews Mobile Layout */
.reviews-container {
    flex-direction: column;
    padding: 0 5% 3rem;
}

/* Contact Page Mobile Layout */
.contact-container {
    flex-direction: column;
    padding: 0 5% 3rem;
}

/* Footer Mobile Layout */
.footer-content {
    flex-direction: column;
}

.footer-section {
    margin: 0 0 2rem 0;
}

/* Modal Mobile Adjustments */
.modal-content {
    max-width: 95%;
    max-height: 70vh;
}

#caption {
    padding: 1rem 5%;
    bottom: 15px;
}

.close {
    top: 15px;
    right: 15px;
    font-size: 2rem;
}

/* Viewport Height Fix for Mobile */
:root {
    --vh: 1vh;
}

/* Prevent zoom on input focus */
@media screen and (max-width: 767px) {
    input, select, textarea {
        font-size: 16px;
    }
}

/* Tablet Styles */
@media (min-width: 768px) {
    .hero {
        flex-direction: row;
        min-height: 80vh;
        padding: calc(var(--header-height) + 2rem) 5% 5rem;
    }

    .hero-content {
        padding-right: 2rem;
        order: 0;
    }

    .hero-image {
        order: 0;
    }

    .features {
        flex-direction: row;
        flex-wrap: wrap;
    }

    .feature-card {
        flex: calc(50% - 2rem);
        margin: 0 1rem 2rem;
    }

    .testimonial-slider {
        flex-direction: row;
    }

    .square-gallery {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    }

    .systems-grid {
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    }

    .special-content {
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
    }

    .special-details {
        flex: 1;
    }

    .filtration-card {
        flex-direction: row;
    }

    .filtration-image-container {
        flex: 1;
        height: auto;
        min-height: 300px;
    }

    .filtration-content {
        flex: 1;
    }
}

/* Desktop Styles */
@media (min-width: 1024px) {
    .hamburger {
        display: none;
    }

    nav ul {
        position: static;
        width: auto;
        height: auto;
        flex-direction: row;
        background-color: transparent;
        padding: 0;
        display: flex;
    }

        nav ul li {
            margin: 0 0 0 2rem;
        }

    .features {
        flex-wrap: nowrap;
    }

    .feature-card {
        flex: 1;
        margin: 0 1rem;
    }
}

/* Large Desktop Styles */
@media (min-width: 1200px) {
    .hero-title {
        font-size: 3rem;
    }

    .hero-image {
        height: 400px;
    }
}

/* Animation Optimizations */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
        scroll-behavior: auto !important;
    }
}

/* iOS Viewport Height Fix */
@supports (-webkit-touch-callout: none) {
    :root {
        --vh: calc(var(--vh, 1vh) * 100);
    }
}

