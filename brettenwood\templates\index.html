{% extends "base.html" %}

{% block content %}
<section class="hero">
    <div class="hero-content">
        <h1 class="hero-title">Reliable Water Backup Solutions for Your Home</h1>
        <p class="hero-subtitle">Professional installation and maintenance services in KwaZulu-Natal</p>
        <div class="hero-cta">
            <a href="{{ url_for('systems') }}" class="cta-button">Explore Our Systems</a>
            <a href="{{ url_for('contact') }}" class="secondary-button">Get a Quote</a>
        </div>
    </div>
</section>

<section class="features container">
    <div class="feature-card">
        <div class="feature-icon">
            <i class="fas fa-tint"></i>
        </div>
        <h3 class="feature-title">Water Backup Systems</h3>
        <p class="feature-text">From 1000L to 5000L JoJo tank systems with professional installation</p>
    </div>
    <div class="feature-card">
        <div class="feature-icon">
            <i class="fas fa-home"></i>
        </div>
        <h3 class="feature-title">Home Maintenance</h3>
        <p class="feature-text">Comprehensive home maintenance services to keep your property in top condition</p>
    </div>
    <div class="feature-card">
        <div class="feature-icon">
            <i class="fas fa-award"></i>
        </div>
        <h3 class="feature-title">Quality Guarantee</h3>
        <p class="feature-text">All our work comes with a satisfaction guarantee and after-service support</p>
    </div>
</section>

<section class="container">
    <div class="section-header">
        <h2>What Our Customers Say</h2>
        <p>See what our satisfied customers have to say about our water backup systems</p>
    </div>
    <div class="reviews-grid">
        {% for review in reviews[:3] %}
        <div class="review-card">
            <div class="review-rating">
                {% for i in range(5) %}
                {% if i < review.rating %}
                <span class="star">★</span>
                {% else %}
                <span class="star">☆</span>
                {% endif %}
                {% endfor %}
            </div>
            <p class="review-text">"{{ review.comment }}"</p>
            <p class="review-author">- {{ review.name }}</p>
        </div>
        {% endfor %}
    </div>
    <div class="text-center mt-2">
        <a href="{{ url_for('reviews') }}" class="btn btn-outline">View All Reviews</a>
    </div>
</section>
{% endblock %}