document.addEventListener('DOMContentLoaded', function () {
    // Mobile menu toggle with touch event support
    const hamburger = document.querySelector('.hamburger');
    const navLinks = document.querySelector('nav ul');

    function toggleMenu() {
        hamburger.classList.toggle('active');
        navLinks.classList.toggle('active');
        document.body.style.overflow = navLinks.classList.contains('active') ? 'hidden' : '';
    }

    // Add both click and touch events for better mobile support
    hamburger.addEventListener('click', toggleMenu);
    hamburger.addEventListener('touchstart', function (e) {
        e.preventDefault();
        toggleMenu();
    });

    // Close menu when tapping outside on mobile
    document.addEventListener('click', function (e) {
        if (navLinks.classList.contains('active') &&
            !e.target.closest('nav') &&
            !e.target.classList.contains('hamburger')) {
            toggleMenu();
        }
    });

    // Smooth scrolling with timeout for mobile browsers
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const targetId = this.getAttribute('href');
            if (targetId === '#') return;

            const targetElement = document.querySelector(targetId);
            if (targetElement) {
                // Close mobile menu if open
                if (hamburger.classList.contains('active')) {
                    toggleMenu();
                }

                // Add slight delay for mobile browsers to handle the scroll properly
                setTimeout(() => {
                    window.scrollTo({
                        top: targetElement.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }, 100);
            }
        });
    });

    // Portfolio Modal with touch support
    window.openModal = function (imageSrc, location, description) {
        const modal = document.getElementById('imageModal');
        const modalImg = document.getElementById('expandedImage');
        const caption = document.getElementById('caption');

        modalImg.src = imageSrc;
        caption.innerHTML = `<h3>${location}</h3><p>${description}</p>`;
        modal.classList.add('show');
        document.body.style.overflow = 'hidden';

        // Prevent modal from closing when touching image
        modalImg.addEventListener('touchstart', function (e) {
            e.stopPropagation();
        });
    };

    window.closeModal = function () {
        const modal = document.getElementById('imageModal');
        modal.classList.remove('show');
        document.body.style.overflow = '';
    };

    // Close modal when clicking/touching outside or pressing ESC
    document.addEventListener('click', function (event) {
        if (event.target.classList.contains('modal')) {
            closeModal();
        }
    });

    document.addEventListener('touchstart', function (event) {
        if (event.target.classList.contains('modal')) {
            closeModal();
        }
    });

    document.addEventListener('keydown', function (event) {
        if (event.key === "Escape") {
            closeModal();
        }
    });

    // Animation on scroll with throttling for performance
    let lastScrollPosition = 0;
    let ticking = false;

    const animateOnScroll = function () {
        const elements = document.querySelectorAll('.feature-card, .system-card, .gallery-square, .review-card');
        const windowHeight = window.innerHeight;

        elements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;

            if (elementPosition < windowHeight - 100) {
                element.style.opacity = '1';
                element.style.transform = 'translateY(0)';
            }
        });

        ticking = false;
    };

    // Initialize animations
    const animatedElements = document.querySelectorAll('.feature-card, .system-card, .gallery-square, .review-card');
    animatedElements.forEach(element => {
        element.style.opacity = '0';
        element.style.transform = 'translateY(20px)';
        element.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
    });

    // Throttled scroll event for better mobile performance
    window.addEventListener('scroll', function () {
        lastScrollPosition = window.scrollY;

        if (!ticking) {
            window.requestAnimationFrame(function () {
                animateOnScroll(lastScrollPosition);
                ticking = false;
            });

            ticking = true;
        }
    });

    // Trigger initial animation check
    animateOnScroll();

    // Brand filtering with touch support
    document.querySelectorAll('.brand-btn').forEach(btn => {
        // Add touch support for buttons
        btn.addEventListener('touchstart', function (e) {
            e.preventDefault();
            this.click();
        });

        btn.addEventListener('click', function () {
            const buttons = document.querySelectorAll('.brand-btn');
            const cards = document.querySelectorAll('.system-card');
            const selectedBrand = this.dataset.brand;

            // Update active button
            buttons.forEach(b => b.classList.remove('active'));
            this.classList.add('active');

            // Filter cards
            cards.forEach(card => {
                const cardBrand = card.dataset.brand;
                const shouldShow = selectedBrand === 'all' || cardBrand === selectedBrand;

                if (shouldShow) {
                    card.style.display = 'block';
                    // Force reflow to enable animation
                    void card.offsetWidth;
                    card.style.opacity = '1';
                    card.style.transform = 'translateY(0)';
                } else {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    // Hide after animation completes
                    setTimeout(() => {
                        card.style.display = 'none';
                    }, 300);
                }
            });
        });
    });

    // Prevent zooming on double-tap for better mobile UX
    document.addEventListener('dblclick', function (e) {
        e.preventDefault();
    }, { passive: false });

    // Viewport height fix for mobile browsers
    function setVh() {
        let vh = window.innerHeight * 0.01;
        document.documentElement.style.setProperty('--vh', `${vh}px`);
    }

    setVh();
    window.addEventListener('resize', setVh);
});

