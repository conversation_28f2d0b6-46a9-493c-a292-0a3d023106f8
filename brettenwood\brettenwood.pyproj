<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>32e65066-07b4-476b-87f1-3ce9bd735b34</ProjectGuid>
    <ProjectHome>.</ProjectHome>
    <StartupFile>app.py</StartupFile>
    <SearchPath>
    </SearchPath>
    <WorkingDirectory>.</WorkingDirectory>
    <OutputPath>.</OutputPath>
    <Name>brettenwood</Name>
    <RootNamespace>brettenwood</RootNamespace>
    <InterpreterId>MSBuild|env|$(MSBuildProjectFullPath)</InterpreterId>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Debug' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)' == 'Release' ">
    <DebugSymbols>true</DebugSymbols>
    <EnableUnmanagedDebugging>false</EnableUnmanagedDebugging>
  </PropertyGroup>
  <ItemGroup>
    <Compile Include="app.py" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="static\images\systems\" />
    <Folder Include="static\images\portfolio\" />
    <Folder Include="templates\" />
    <Folder Include="static\" />
    <Folder Include="static\css\" />
    <Folder Include="static\js\" />
    <Folder Include="static\images\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="requirements.txt" />
    <Content Include="static\css\style.css" />
    <Content Include="static\images\portfolio\job1.jpg" />
    <Content Include="static\images\portfolio\job3.jpg" />
    <Content Include="static\images\portfolio\job2.jpg" />
    <Content Include="static\images\systems\eco-1000.jpg" />
    <Content Include="static\images\systems\eco-2500.png" />
    <Content Include="static\images\systems\eco-5000.png" />
    <Content Include="static\images\systems\filtration-system.jpg" />
    <Content Include="static\images\systems\jojo-1000.png" />
    <Content Include="static\images\systems\jojo-2500.webp" />
    <Content Include="static\images\systems\jojo-5200.webp" />
    <Content Include="static\js\main.js" />
    <Content Include="templates\about.html" />
    <Content Include="templates\base.html" />
    <Content Include="templates\contact.html" />
    <Content Include="templates\index.html" />
    <Content Include="templates\portfolio.html" />
    <Content Include="templates\reviews.html" />
    <Content Include="templates\systems.html" />
  </ItemGroup>
  <ItemGroup>
    <Interpreter Include="env\">
      <Id>env</Id>
      <Version>3.13</Version>
      <Description>env (Python 3.13 (64-bit))</Description>
      <InterpreterPath>Scripts\python.exe</InterpreterPath>
      <WindowsInterpreterPath>Scripts\pythonw.exe</WindowsInterpreterPath>
      <PathEnvironmentVariable>PYTHONPATH</PathEnvironmentVariable>
      <Architecture>X64</Architecture>
    </Interpreter>
  </ItemGroup>
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)\Python Tools\Microsoft.PythonTools.targets" />
  <!-- Uncomment the CoreCompile target to enable the Build command in
       Visual Studio and specify your pre- and post-build commands in
       the BeforeBuild and AfterBuild targets below. -->
  <!--<Target Name="CoreCompile" />-->
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
</Project>