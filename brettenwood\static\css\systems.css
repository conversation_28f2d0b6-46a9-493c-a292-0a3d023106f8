/* Systems Page Styles */
:root {
    --primary: #000000;
    --secondary: #ffffff;
    --accent: #555555;
    --light-gray: #f5f5f5;
    --dark-gray: #333333;
    --transition: all 0.3s ease;
    --header-height: 70px;
}

/* TEST - Very obvious styling to verify CSS is working */
body {
    background-color: #f0f8ff !important;
}

.systems-header {
    background-color: #ffeb3b !important;
    border: 5px solid red !important;
}

/* Systems Header */
.systems-header {
    padding: calc(var(--header-height) + 2rem) 5% 2rem;
    text-align: center;
}

.systems-header h1 {
    font-size: 2rem;
    margin-bottom: 1rem;
    color: var(--primary);
}

.systems-header p {
    color: var(--accent);
    font-size: 1.1rem;
}

/* Brand Toggle */
.brand-toggle {
    display: flex;
    justify-content: center;
    padding: 2rem 5% 1rem;
    margin-bottom: 2rem;
}

.brand-toggle-inner {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    background: var(--light-gray);
    padding: 0.5rem;
    border-radius: 50px;
}

.brand-btn {
    padding: 0.8rem 1.5rem;
    font-size: 0.9rem;
    border: none;
    background: transparent;
    color: var(--accent);
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 500;
    min-width: 80px;
}

.brand-btn:hover {
    background: var(--secondary);
    color: var(--primary);
}

.brand-btn.active {
    background: var(--primary);
    color: var(--secondary);
}

/* Systems Grid */
.systems-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
    padding: 2rem 5%;
}

/* System Card */
.system-card {
    background: var(--secondary);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    transition: var(--transition);
    position: relative;
}

.system-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: var(--primary);
    color: var(--secondary);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    z-index: 2;
}

.system-image-container {
    height: 250px;
    position: relative;
    overflow: hidden;
}

.system-image {
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
}

.system-details {
    padding: 1.5rem;
}

.system-details h2 {
    font-size: 1.3rem;
    margin-bottom: 1rem;
    color: var(--primary);
}

.specs {
    margin-bottom: 1.5rem;
}

.specs p {
    margin-bottom: 0.5rem;
    color: var(--accent);
}

.price-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.price {
    font-size: 1.5rem;
    font-weight: bold;
    color: var(--primary);
}

.inquiry-button {
    background: var(--primary);
    color: var(--secondary);
    padding: 0.8rem 1.5rem;
    border-radius: 25px;
    text-decoration: none;
    font-weight: 500;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

/* Media Queries */
@media (min-width: 768px) {
    .systems-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .systems-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
